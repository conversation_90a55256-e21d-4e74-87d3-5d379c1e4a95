import { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';
import {
  cleanTestDatabase,
  closeTestDatabase,
  createTestDatabase,
  seedTestDatabase,
  TestDatabase
} from '../../test/database-setup.js';
import { GlobalExceptionFilter } from '../common/filters/global-exception.filter.js';
import { UsersService } from '../users/users.service.js';
import { AuthController } from './auth.controller.js';
import { AuthService } from './auth.service.js';

describe('AuthController Integration Tests', () => {
  let app: INestApplication;
  let testDb: TestDatabase;
  let testData: any;

  beforeAll(async () => {
    // Create test database
    testDb = await createTestDatabase();
    testData = await seedTestDatabase(testDb);

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        AuthService,
        UsersService,
        {
          provide: 'DB',
          useValue: testDb,
        },
        {
          provide: JwtService,
          useValue: {
            signAsync: jest.fn().mockImplementation((payload) =>
              Promise.resolve(`mock.jwt.token.${payload.sub}`)
            ),
            verify: jest.fn().mockImplementation((token) => {
              const parts = token.split('.');
              if (parts.length === 4 && parts[0] === 'mock') {
                return {
                  sub: parts[3],
                  email: '<EMAIL>',
                  roles: ['user'],
                  sid: 'test-session-id',
                };
              }
              throw new Error('Invalid token');
            }),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key, defaultValue) => {
              const config = {
                'JWT_SECRET': 'test-secret',
                'JWT_EXPIRES_IN': '15m',
                'JWT_REFRESH_SECRET': 'test-refresh-secret',
                'JWT_REFRESH_EXPIRES_IN': '7d',
                'NODE_ENV': 'test',
              };
              return config[key] || defaultValue;
            }),
          },
        },
      ],
    }).compile();

    app = module.createNestApplication();
    app.useGlobalFilters(new GlobalExceptionFilter());
    await app.init();
  });

  afterAll(async () => {
    await app.close();
    await closeTestDatabase();
  });

  beforeEach(async () => {
    await cleanTestDatabase(testDb);
    testData = await seedTestDatabase(testDb);
  });

  describe('POST /auth/register', () => {
    it('should register a new user successfully', async () => {
      const registerData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        firstName: 'New',
        lastName: 'User',
      };

      const response = await request(app.getHttpServer())
        .post('/auth/register')
        .send(registerData)
        .expect(201);

      expect(response.body).toMatchObject({
        user: {
          email: registerData.email,
          firstName: registerData.firstName,
          lastName: registerData.lastName,
          roles: ['user'],
          emailVerified: false,
          twoFactorEnabled: false,
        },
        tokens: {
          accessToken: expect.any(String),
          refreshToken: expect.any(String),
        },
      });

      expect(response.body.user.id).toBeDefined();
      expect(response.body.user.createdAt).toBeDefined();
    });

    it('should return 409 for existing email', async () => {
      const registerData = {
        email: testData.testUser.email,
        password: 'SecurePassword123!',
        firstName: 'Test',
        lastName: 'User',
      };

      const response = await request(app.getHttpServer())
        .post('/auth/register')
        .send(registerData)
        .expect(409);

      expect(response.body.message).toContain('already exists');
    });

    it('should return 400 for invalid email format', async () => {
      const registerData = {
        email: 'invalid-email',
        password: 'SecurePassword123!',
        firstName: 'Test',
        lastName: 'User',
      };

      await request(app.getHttpServer())
        .post('/auth/register')
        .send(registerData)
        .expect(400);
    });

    it('should return 400 for weak password', async () => {
      const registerData = {
        email: '<EMAIL>',
        password: '123',
        firstName: 'Test',
        lastName: 'User',
      };

      await request(app.getHttpServer())
        .post('/auth/register')
        .send(registerData)
        .expect(400);
    });
  });

  describe('POST /auth/login', () => {
    it('should login user successfully', async () => {
      const loginData = {
        email: testData.testUser.email,
        password: 'password123',
      };

      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toMatchObject({
        user: {
          id: testData.testUser.id,
          email: testData.testUser.email,
          firstName: 'Test',
          lastName: 'User',
        },
        tokens: {
          accessToken: expect.any(String),
          refreshToken: expect.any(String),
        },
      });
    });

    it('should return 401 for invalid credentials', async () => {
      const loginData = {
        email: testData.testUser.email,
        password: 'wrongpassword',
      };

      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.message).toContain('Invalid credentials');
    });

    it('should return 401 for non-existent user', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      await request(app.getHttpServer())
        .post('/auth/login')
        .send(loginData)
        .expect(401);
    });
  });

  describe('POST /auth/logout', () => {
    it('should logout user successfully', async () => {
      // First login to get session
      const loginResponse = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testData.testUser.email,
          password: 'password123',
        });

      const sessionId = loginResponse.body.sessionId;

      const response = await request(app.getHttpServer())
        .post('/auth/logout')
        .send({ sessionId })
        .expect(200);

      expect(response.body.message).toContain('successfully');
    });

    it('should return 400 when no session ID provided', async () => {
      await request(app.getHttpServer())
        .post('/auth/logout')
        .send({})
        .expect(400);
    });
  });

  describe('POST /auth/refresh', () => {
    it('should refresh tokens successfully', async () => {
      // First login to get refresh token
      const loginResponse = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testData.testUser.email,
          password: 'password123',
        });

      const refreshToken = loginResponse.body.tokens.refreshToken;

      const response = await request(app.getHttpServer())
        .post('/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body).toMatchObject({
        accessToken: expect.any(String),
        refreshToken: expect.any(String),
      });
    });

    it('should return 401 for invalid refresh token', async () => {
      await request(app.getHttpServer())
        .post('/auth/refresh')
        .send({ refreshToken: 'invalid-token' })
        .expect(401);
    });
  });

  describe('POST /auth/forgot-password', () => {
    it('should handle forgot password request', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({ email: testData.testUser.email })
        .expect(200);

      expect(response.body.message).toContain('reset instructions');
    });

    it('should return same message for non-existent email (security)', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({ email: '<EMAIL>' })
        .expect(200);

      expect(response.body.message).toContain('reset instructions');
    });
  });

  describe('POST /auth/reset-password', () => {
    it('should reset password with valid token', async () => {
      // First request password reset
      await request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({ email: testData.testUser.email });

      // In a real scenario, we'd get the token from email
      // For testing, we'll use a mock token
      const response = await request(app.getHttpServer())
        .post('/auth/reset-password')
        .send({
          token: 'mock-reset-token',
          newPassword: 'NewSecurePassword123!',
        })
        .expect(400); // Expected to fail with mock token

      expect(response.body.message).toContain('Invalid');
    });
  });
});

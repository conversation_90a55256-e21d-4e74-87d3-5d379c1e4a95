import { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import * as request from 'supertest';
import {
  cleanTestDatabase,
  closeTestDatabase,
  createTestDatabase,
  seedTestDatabase,
  TestDatabase
} from '../../test/database-setup.js';
import { AuthService } from '../auth/auth.service.js';
import { GlobalExceptionFilter } from '../common/filters/global-exception.filter.js';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard.js';
import { UsersController } from './users.controller.js';
import { UsersService } from './users.service.js';

describe('UsersController Integration Tests', () => {
  let app: INestApplication;
  let testDb: TestDatabase;
  let testData: any;
  let jwtService: JwtService;
  let authToken: string;

  beforeAll(async () => {
    // Create test database
    testDb = await createTestDatabase();
    testData = await seedTestDatabase(testDb);

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        UsersService,
        AuthService,
        JwtService,
        {
          provide: 'DB',
          useValue: testDb,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key, defaultValue) => {
              const config = {
                'JWT_SECRET': 'test-secret',
                'JWT_EXPIRES_IN': '15m',
                'JWT_REFRESH_SECRET': 'test-refresh-secret',
                'JWT_REFRESH_EXPIRES_IN': '7d',
                'NODE_ENV': 'test',
              };
              return config[key] || defaultValue;
            }),
          },
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: (context) => {
          const request = context.switchToHttp().getRequest();
          request.user = { userId: testData.testUser.id };
          return true;
        },
      })
      .compile();

    app = module.createNestApplication();
    app.useGlobalFilters(new GlobalExceptionFilter());
    await app.init();

    jwtService = module.get<JwtService>(JwtService);
    authToken = jwtService.sign({ userId: testData.testUser.id });
  });

  afterAll(async () => {
    await app.close();
    await closeTestDatabase();
  });

  beforeEach(async () => {
    await cleanTestDatabase(testDb);
    testData = await seedTestDatabase(testDb);
  });

  describe('GET /users/me', () => {
    it('should get current user profile', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: testData.testUser.id,
        email: testData.testUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: ['user'],
      });
    });

    it('should return 401 without auth token', async () => {
      await request(app.getHttpServer())
        .get('/users/me')
        .expect(401);
    });
  });

  describe('PUT /users/me', () => {
    it('should update user profile successfully', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name',
        avatar: 'new-avatar-url',
      };

      const response = await request(app.getHttpServer())
        .put('/users/me')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject({
        id: testData.testUser.id,
        firstName: updateData.firstName,
        lastName: updateData.lastName,
        avatar: updateData.avatar,
      });
    });

    it('should return 400 for invalid data', async () => {
      const updateData = {
        firstName: '', // Invalid empty string
      };

      await request(app.getHttpServer())
        .put('/users/me')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(400);
    });
  });

  describe('PUT /users/me/profile', () => {
    it('should update user profile with bio', async () => {
      const updateData = {
        name: 'Updated Full Name',
        email: '<EMAIL>',
        bio: 'Updated bio text',
      };

      const response = await request(app.getHttpServer())
        .put('/users/me/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject({
        id: testData.testUser.id,
        firstName: 'Updated',
        lastName: 'Full Name',
        bio: updateData.bio,
      });
    });

    it('should return 409 for duplicate email', async () => {
      const updateData = {
        email: testData.adminUser.email, // Use admin's email
      };

      await request(app.getHttpServer())
        .put('/users/me/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(409);
    });
  });

  describe('PUT /users/me/password', () => {
    it('should change password successfully', async () => {
      const passwordData = {
        currentPassword: 'password123',
        newPassword: 'NewSecurePassword123!',
      };

      const response = await request(app.getHttpServer())
        .put('/users/me/password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(passwordData)
        .expect(200);

      expect(response.body.message).toContain('successfully');
    });

    it('should return 400 for incorrect current password', async () => {
      const passwordData = {
        currentPassword: 'wrongpassword',
        newPassword: 'NewSecurePassword123!',
      };

      await request(app.getHttpServer())
        .put('/users/me/password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(passwordData)
        .expect(400);
    });

    it('should return 400 for weak new password', async () => {
      const passwordData = {
        currentPassword: 'password123',
        newPassword: '123', // Too weak
      };

      await request(app.getHttpServer())
        .put('/users/me/password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(passwordData)
        .expect(400);
    });
  });

  describe('GET /users/me/devices', () => {
    it('should get user devices', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/me/devices')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('POST /users/me/devices', () => {
    it('should register a new device', async () => {
      const deviceData = {
        deviceName: 'Test Device',
        deviceType: 'mobile',
        platform: 'iOS',
        deviceInfo: 'iPhone 12',
      };

      const response = await request(app.getHttpServer())
        .post('/users/me/devices')
        .set('Authorization', `Bearer ${authToken}`)
        .send(deviceData)
        .expect(201);

      expect(response.body).toMatchObject({
        deviceName: deviceData.deviceName,
        deviceType: deviceData.deviceType,
        platform: deviceData.platform,
        isTrusted: false,
      });
    });

    it('should return 400 for invalid device data', async () => {
      const deviceData = {
        deviceName: '', // Invalid empty name
        deviceType: 'mobile',
      };

      await request(app.getHttpServer())
        .post('/users/me/devices')
        .set('Authorization', `Bearer ${authToken}`)
        .send(deviceData)
        .expect(400);
    });
  });

  describe('GET /users/me/sessions', () => {
    it('should get user sessions', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/me/sessions')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('POST /users/me/2fa/setup', () => {
    it('should setup 2FA for user', async () => {
      const response = await request(app.getHttpServer())
        .post('/users/me/2fa/setup')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toMatchObject({
        secret: expect.any(String),
        qrCodeUrl: expect.any(String),
        manualEntryKey: expect.any(String),
        issuer: 'RSGlider',
      });
    });

    it('should return 400 if 2FA already enabled', async () => {
      // First setup 2FA
      await request(app.getHttpServer())
        .post('/users/me/2fa/setup')
        .set('Authorization', `Bearer ${authToken}`);

      // Try to setup again
      await request(app.getHttpServer())
        .post('/users/me/2fa/setup')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });
  });

  describe('Role Management', () => {
    it('should get user roles', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/me/roles')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body).toContain('user');
    });
  });
});
